<script setup lang="ts">
import { ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { analyzeApi } from '~/api/analyze'
import type { OeeResult } from '~/api/analyze/type'
import { TriggerRecordApi } from '~/api/feedback/trigger'
import { useAnalyticsSearchSchema } from '~/pages/dashboard/schema'

const props = defineProps<{
  code: string
  startTime: Date
  endTime: Date
}>()

interface LineStatus {
  anomaliesName: string
  triggerTime: Date | null
}

const router = useRouter()

// 状态管理
const form = useAnalyticsSearchSchema()
const loading = ref<boolean>(false)

// 数据状态
const oee = ref<OeeResult>()
const lineStatus = ref<LineStatus>({
  anomaliesName: '正常',
  triggerTime: null,
})

// API 调用封装
const loadData = form.handleSubmit(async (values) => {
  loading.value = true
  try {
    const res = await analyzeApi.getLineOee(values)
    oee.value = res
  }
  finally {
    loading.value = false
  }
})

const loadProductionLineStatus = form.handleSubmit(async (values) => {
  loading.value = true
  try {
    const res = await TriggerRecordApi.latestOpenException(values.code)
    if (res) {
      lineStatus.value = {
        anomaliesName: res.anomaliesName,
        triggerTime: res.triggerTime,
      }
    }
    else {
      lineStatus.value = {
        anomaliesName: '正常',
        triggerTime: null,
      }
    }
  }
  finally {
    loading.value = false
  }
})

// 生命周期和监听器
watch(props, (newProps) => {
  if (newProps) {
    // 设置产品线
    form.setFieldValue('code', newProps.code)
    form.setFieldValue('startTime', newProps.startTime)
    form.setFieldValue('endTime', newProps.endTime)
    // 加载数据
    loadData()
    // 加载线体状态
    loadProductionLineStatus()
  }
}, { immediate: true, deep: true })
</script>

<template>
  <div class="w-full">
    <div class="grid grid-cols-[20%_80%] grid-rows-1 gap-1 rounded-lg from-gray-700 via-gray-900 to-black bg-gradient-to-r pt-2 shadow-lg">
      <!-- 点击线体跳转 -->
      <LineStatusCard
        :code="props.code"
        :anomalies-name="lineStatus.anomaliesName"
        :trigger-time="lineStatus.triggerTime"
        @click="router.push({ name: 'dashboard', params: { code: props.code } })"
      />
      <div class="grid grid-cols-12 gap-1">
        <MetricCard title="开班时间" :value="`${oee?.actualPlanTime ?? 0} h`" class="col-span-2" />
        <MetricCard title="运机时间" :value="`${oee?.runTime ?? 0} h`" class="col-span-2" />
        <MetricCard title="停机时间" :value="`${oee?.stopTime ?? 0} h`" class="col-span-2" />
        <MetricCard title="标准生产数量 | 实际生产数量" :value="`${oee?.planBoard ?? 0} | ${oee?.actualBoard ?? 0}`" class="col-span-3" />
        <MetricCard title="不良品数" :value="oee?.defectCount ?? 0" class="col-span-1" />
        <MetricCard title="换线次数 | 换线时间" :value="`${oee?.changeoverNum ?? 0} | ${oee?.changeoverTime ?? 0}`" class="col-span-2" />
      </div>

      <div class="grid grid-cols-4 gap-1">
        <OeeMetricCard title="目标运转率 | 实际运转率" :target-value="oee?.availabilityTarget ?? 0" :actual-value="oee?.availability ?? 0" />
        <OeeMetricCard title="目标有效生产率 | 实际有效生产率" :target-value="oee?.performanceTarget ?? 0" :actual-value="oee?.performance ?? 0" />
        <OeeMetricCard title="目标良品率 | 实际良品率" :target-value="oee?.qualityTarget ?? 0" :actual-value="oee?.quality ?? 0" />
        <OeeMetricCard title="目标OEE | 实际OEE" :target-value="oee?.oeeTarget ?? 0" :actual-value="oee?.oee ?? 0" />
      </div>
    </div>
  </div>
</template>

<style scoped>
</style>
