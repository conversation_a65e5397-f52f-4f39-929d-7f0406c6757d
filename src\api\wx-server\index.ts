import type { TreeNode, User } from './types'
import { kyGet } from '~/utils/request'

export const wxServerApi = {
  departMentList: () => kyGet(`wx-server/getDepartmentList/cost-analysis`).json<TreeNode[]>(),
  userListByDept: (deptId: number) => kyGet(`wx-server/getUserList/cost-analysis`, { departmentId: deptId }).json<User[]>(),
  userListByName: (name: string) => kyGet(`wx-server/getUserListByName/cost-analysis`, { name }).json<User[]>(),
  userTreeList: () => kyGet(`wx-server/getUserTreeList/cost-analysis`).json<TreeNode[]>(),
}
