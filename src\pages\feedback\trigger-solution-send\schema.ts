import { toTypedSchema } from '@vee-validate/zod'
import { useForm } from 'vee-validate'
import { z } from '~/utils/zext'
import type { FeedbackTriggerSolutionSendSearchParam } from '~/api/feedback/trigger-solution-send/type'

const TriggerSolutionSendSearchSchema = toTypedSchema(
  z.object({
    triggerSolutionId: z.string().optional(),
    triggerRecordId: z.string().optional(),
    triggerSendId: z.string().optional(),
    sendStatus: z.boolean().optional(),
    messageType: z.string().optional(),
    expectedSendStartTime: z.date().optional(),
    expectedSendEndTime: z.date().optional(),
    sendStartTime: z.date().optional(),
    sendEndTime: z.date().optional(),
  }),
)

const TriggerSolutionSendCreateSchema = toTypedSchema(
  z.object({
    expectedSendTime: z.date({
      required_error: '请选择预期发送时间',
    }),
    sendUserId: z.array(z.string().min(1)).min(1, '至少选择一个发送人').default([]),
    reportUserId: z.array(z.string().min(1)).min(1, '至少选择一个告知人').default([]),
    triggerSolutionId: z.string().min(1, '请输入解决方案ID'),
    triggerRecordId: z.string().min(1, '请输入触发记录ID'),
    triggerSendId: z.string().min(1, '请输入发送记录ID'),
    sendInfo: z.string().optional(),
    sendStatus: z.boolean().default(false),
    messageType: z.string().min(1, '请选择消息类型'),
  }),
)

const TriggerSolutionSendUpdateSchema = toTypedSchema(
  z.object({
    expectedSendTime: z.date({
      required_error: '请选择预期发送时间',
    }),
    sendTime: z.date().optional(),
    sendUserId: z.array(z.string().min(1)).min(1, '至少选择一个发送人').default([]),
    reportUserId: z.array(z.string().min(1)).min(1, '至少选择一个告知人').default([]),
    triggerSolutionId: z.string().min(1, '请输入解决方案ID'),
    triggerRecordId: z.string().min(1, '请输入触发记录ID'),
    triggerSendId: z.string().min(1, '请输入发送记录ID'),
    sendInfo: z.string().optional(),
    sendResult: z.string().optional(),
    sendStatus: z.boolean().default(false),
    messageType: z.string().min(1, '请选择消息类型'),
  }),
)

export function useFeedbackTriggerSolutionSendSearchForm() {
  const form = useForm<FeedbackTriggerSolutionSendSearchParam>({
    validationSchema: TriggerSolutionSendSearchSchema,
  })
  return form
}

export function useFeedbackTriggerSolutionSendCreateForm() {
  const form = useForm({
    validationSchema: TriggerSolutionSendCreateSchema,
  })
  return form
}

export function useFeedbackTriggerSolutionSendUpdateForm() {
  const form = useForm({
    validationSchema: TriggerSolutionSendUpdateSchema,
  })
  return form
}
