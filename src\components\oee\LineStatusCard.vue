<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps<{
  code: string
  anomaliesName: string
  triggerTime: Date | null
}>()

const formattedTriggerTime = computed(() => {
  if (props.triggerTime) {
    return new Date(props.triggerTime).toLocaleString([], { month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit' })
  }
  return ''
})

const statusClass = computed(() => {
  return props.anomaliesName === '正常' ? 'text-green-400' : 'text-red-400'
})
</script>

<template>
  <div
    class="row-span-2 flex flex-col transform cursor-pointer items-center justify-center transition-transform panel hover:scale-105"
  >
    <span class="text-2xl text-white">线体</span>
    <span class="text-3xl text-blue-500 font-bold">{{ code }}</span>
    <div class="mt-1 flex flex-col items-center">
      <span v-if="triggerTime" class="block text-gray-300">
        {{ formattedTriggerTime }}
      </span>
      <span :class="statusClass">
        {{ anomaliesName }}
      </span>
    </div>
  </div>
</template>

<style scoped>
.panel {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 8px;
  transition: transform 0.3s ease;
}
.panel:hover {
  transform: scale(1.05);
}
</style>
