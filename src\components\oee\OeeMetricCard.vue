<script setup lang="ts">
defineProps<{
  title: string
  targetValue: string | number
  actualValue: string | number
}>()
</script>

<template>
  <div class="w-full flex flex-col transform items-center justify-center rounded-lg bg-gray-800 py-1 shadow-md transition-transform panel hover:scale-105">
    <span class="text-sm text-white">{{ title }}</span>
    <span class="text-xl text-primary font-bold">{{ targetValue }}% | {{ actualValue }}%</span>
  </div>
</template>

<style scoped>
.panel {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 8px;
  transition: transform 0.3s ease;
}
.panel:hover {
  transform: scale(1.05);
}
</style>
