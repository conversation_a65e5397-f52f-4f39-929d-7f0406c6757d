<script setup lang="ts">
import { useRoute } from 'vue-router'
import { analyzeApi } from '~/api/analyze'
import { productLineApi } from '~/api/line'
import type { ProductLine } from '~/api/line/types'
import LineContainer from '~/components/oee/LineContainer.vue'
import { useAggregationStore } from '~/stores/aggregation'
import Honghong from '~/assets/honghong.gif'

const startTime = ref<Date>()
const endTime = ref<Date>()
const route = useRoute()
const loading = ref<boolean>(false)
const aggregationStore = useAggregationStore()
const workshopCode = ref<string>()
const refreshInterval = ref<ReturnType<typeof setInterval>>()
const el = useTemplateRef<HTMLElement>('el')
const { width, height } = useWindowSize()
const { style } = useDraggable(el, {
  initialValue: { x: width.value * 0.01, y: height.value * 0.75 },
})

// 数据状态
const lineList = ref<ProductLine[]>([])

// 加载数据
async function loadData(code: string) {
  loading.value = true
  try {
    const res = await productLineApi.listByWorkShopId(code)
    lineList.value = res
  }
  finally {
    loading.value = false
  }
}

// 日期处理方法
async function initializeDates() {
  try {
    const serverTime = await analyzeApi.getServerTime()
    const now = new Date(serverTime)
    const currentDay = new Date(now)
    const nextDay = new Date(now)
    nextDay.setDate(now.getDate() + 1)

    startTime.value = new Date(currentDay.setHours(8, 0, 0, 0))
    endTime.value = new Date(nextDay.setHours(8, 0, 0, 0))
  }
  catch (error) {
    console.error('Failed to get server time:', error)
    // 使用当前时间
    const now = new Date()
    const currentDay = new Date(now)
    const nextDay = new Date(now)
    nextDay.setDate(now.getDate() + 1)

    startTime.value = new Date(currentDay.setHours(8, 0, 0, 0))
    endTime.value = new Date(nextDay.setHours(8, 0, 0, 0))
  }
}

function checkAndUpdateDates() {
  const now = new Date()
  if (!endTime.value || now > endTime.value) {
    initializeDates()
  }
}

watch(() => route.params.code, (newCode) => {
  if (newCode) {
    const code = Array.isArray(route.params.code) ? route.params.code[0] : route.params.code
    workshopCode.value = code
    initializeDates()
    loadData(code)
  }
}, { immediate: true })

onBeforeMount(() => {
  // 初始化日期
  // initializeDates()
  // // 搜索
  // if (workshopCode.value)
  //   loadData(workshopCode.value)
  // 设置自动刷新
  refreshInterval.value = setInterval(() => {
    checkAndUpdateDates()
    if (workshopCode.value)
      loadData(workshopCode.value)
  }, 300000) // 5分钟刷新一次
})

onBeforeUnmount(() => {
  // 清除自动刷新
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value)
  }
})

// 重置时间
function resetDates() {
  initializeDates()
}
</script>

<template>
  <div class="w-full" :class="{ light: !isDark, dark: isDark }">
    <img ref="el" :src="Honghong" alt="Logo" class="fixed z-99 w-32 cursor-grab" :style="style">
    <form v-show="aggregationStore.isSearchVisible" class="mt-2 flex justify-end gap-4 px-8">
      <DatePicker
        v-model="startTime" fluid show-icon date-format="yy-mm-dd"
        v-bind="{ showTime: true, hourFormat: '24' }"
      >
        <template #inputicon="slotProps">
          <i class="pi pi-clock" @click="slotProps.clickCallback" />
        </template>
      </DatePicker>
      <DatePicker
        v-model="endTime" fluid show-icon date-format="yy-mm-dd"
        v-bind="{ showTime: true, hourFormat: '24' }"
      >
        <template #inputicon="slotProps">
          <i class="pi pi-clock" @click="slotProps.clickCallback" />
        </template>
      </DatePicker>
      <Button size="small" icon="pi pi-refresh" @click="resetDates" />
    </form>

    <div class="flex flex-col items-center">
      <LineContainer
        v-for="item in lineList"
        :key="item.code"
        :code="item.code"
        :start-time="startTime!"
        :end-time="endTime!"
      />
    </div>
  </div>
</template>
